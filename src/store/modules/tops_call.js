import is from 'is_js';
import StandardRequest from '@/api/StandardRequest.js';

import {
  get,
  set,
  has,
  forEach,
  toNumber,
  castArray
} from 'lodash';

import {
  simpleDate,
  fullDateTime,
  prepareApiData
} from '@/utils/filters.js';

const state = {};

const getters = {};

const actions = {
  TOPSCALL__add (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CreateNewCall',
      data: prepareApiData(props.data)
    })
    .success(props.callback);
  },

  TOPSCALL__getDefaultsForNew (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetNewCallDefaults',
      data: { CustomerKey: props.customerKey }
    })
    .responseMiddleware(response => {
      if (has(response, 'bNoCharge')) response.bNoCharge = response.bNoCharge === '1';
      if (has(response, 'bSecondCommission')) response.bSecondCommission = response.bSecondCommission === '1';
      if (has(response, 'bPortalToPortal')) response.bPortalToPortal = response.bPortalToPortal === '1';
      if (has(response, 'bMileageRequired')) response.bMileageRequired = response.bMileageRequired === '1';
      if (has(response, 'bTransferredToFES')) response.bTransferredToFES = [1, '1', true, 'true'].includes(response.bTransferredToFES);
      if (has(response, 'bTaxExemptCustomer')) response.bTaxExemptCustomer = response.bTaxExemptCustomer === '1';
      if (has(response, 'bTaxExemptSaleCustomer')) response.bTaxExemptSaleCustomer = response.bTaxExemptSaleCustomer === '1';
    })
    .success(props.callback);
  },

  TOPSCALL__getPricingForNew (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetNewCallPricing',
      data: props.data
    })
    .success(props.callback);
  },

  TOPSCALL__getPricingAfterChange (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'UpdateCallPricingAfterChange',
      data: { ...{ FieldId: props.fieldId, OldValue: props.oldValue, NewValue: props.newValue, KeepCallPricingItems: props.keepExistingItems }, ...props.data }
    })
    .success(props.callback);
  },

  TOPSCALL__getAvailableServices (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetAvailableServices',
      data: {
        CallKey: props.callKey,
        OrderType: props.orderType, // ['T' = Tow, 'S' = Sale, 'A' = Adjustment]
        Filter: get(props, 'filter', 'Basic Services') // ['Basic Services', 'Customer Services', 'All Services']
      }
    })
    .success(props.callback);
  },

  TOPSCALL__getAvailableServicesForNew (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetAvailableServicesForNewCall',
      data: {
        lTowTypeKey: props.towTypeKey,
        lSubterminalKey: props.subterminalKey,
        lCustomerKey: props.customerKey,
        Filter: props.filters
      }
    })
    .success(props.callback);
  },

  TOPSCALL__getServicePricing (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetServicePricing',
      data: {
        CallKey: props.callKey,
        OrderType: props.orderType,
        ServiceKey: props.serviceKey
      }
    })
    .success(props.callback);
  },

  TOPSCALL__getServicePricingForNew (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetServicePricingForNewCall',
      data: {
        lTowTypeKey: props.towTypeKey,
        lSubterminalKey: props.subterminalKey,
        lCustomerKey: props.customerKey,
        ServiceKey: props.serviceKey
      }
    })
    .success(props.callback);
  },

  TOPSCALL__getOrderPricing (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CalculateOrderPricing',
      data: {
        CallKey: props.callKey,
        OrderType: props.orderType,
        OrderLines: props.orderLines,
        TaxRateOverride: props.taxRateOverride
      }
    })
    .responseMiddleware(response => {
      // if ('TaxRate' in response) response.TaxRate = Number(response.TaxRate);
      // if ('TaxRateOverride' in response) response.TaxRateOverride = Number(response.TaxRateOverride);
      // if ('DiscountPct' in response) response.DiscountPct = Number(response.DiscountPct);
      // if ('Total' in response) response.Total = Number(response.Total);
      // if ('TaxTotal' in response) response.TaxTotal = Number(response.TaxTotal);
      // if ('DiscountTotal' in response) response.DiscountTotal = Number(response.DiscountTotal);

      if ('OrderLines' in response) {
        response.OrderLines = response.OrderLines.map(orderLine => {
          // orderLine.uuid = `CalculateOrderPricing_${orderLine.lOrderLineKey}`;

          if ('lOrderLineKey' in orderLine) orderLine.lOrderLineKey = Number(orderLine.lOrderLineKey);
          if ('lCallKey' in orderLine) orderLine.lCallKey = Number(orderLine.lCallKey);
          if ('lDispatchKey' in orderLine) orderLine.lDispatchKey = Number(orderLine.lDispatchKey);
          if ('lServicePricingTypeKey' in orderLine) orderLine.lServicePricingTypeKey = Number(orderLine.lServicePricingTypeKey);
          if ('lServiceKey' in orderLine) orderLine.lServiceKey = Number(orderLine.lServiceKey);
          if ('tDisplayOrder' in orderLine) orderLine.tDisplayOrder = Number(orderLine.tDisplayOrder);
          if ('pQty' in orderLine) orderLine.pQty = Number(orderLine.pQty);
          // if ('lInitialUnitTypeKey' in orderLine) orderLine.lInitialUnitTypeKey = Number(orderLine.lInitialUnitTypeKey);
          // if ('fInitialQty' in orderLine) orderLine.fInitialQty = Number(orderLine.fInitialQty);
          // if ('scInitialRate' in orderLine) orderLine.scInitialRate = Number(orderLine.scInitialRate);
          if ('bInitialFlatRate' in orderLine) orderLine.bInitialFlatRate = orderLine.bInitialFlatRate === '1';
          // if ('scSecondaryRate' in orderLine) orderLine.scSecondaryRate = Number(orderLine.scSecondaryRate);
          if ('bRatesOverridable' in orderLine) orderLine.bRatesOverridable = orderLine.bRatesOverridable === '1';
          // if ('vc100Description' in orderLine) orderLine.vc100Description = orderLine.vc100Description;
          if ('bRetow' in orderLine) orderLine.bRetow = orderLine.bRetow === '1';
          if ('bTaxable' in orderLine) orderLine.bTaxable = orderLine.bTaxable === '1';
          if ('bDiscountable' in orderLine) orderLine.bDiscountable = orderLine.bDiscountable === '1';
          if ('bCommissionable' in orderLine) orderLine.bCommissionable = orderLine.bCommissionable === '1';
          if ('fInitialQtyApplied' in orderLine) orderLine.fInitialQtyApplied = Number(orderLine.fInitialQtyApplied);
          if ('pSecondaryQtyApplied' in orderLine) orderLine.pSecondaryQtyApplied = Number(orderLine.pSecondaryQtyApplied);
          if ('tcTotalPrice' in orderLine) orderLine.tcTotalPrice = Number(orderLine.tcTotalPrice);
          if ('bCalculated' in orderLine) orderLine.bCalculated = orderLine.bCalculated === '1';
          // if ('fMinimumQty' in orderLine) orderLine.fMinimumQty = Number(orderLine.fMinimumQty);
          if ('lUserKey' in orderLine) orderLine.lUserKey = Number(orderLine.lUserKey);
          // if ('dLastModified' in orderLine) orderLine.dLastModified = orderLine.dLastModified;
          if ('bActive' in orderLine) orderLine.bActive = orderLine.bActive === '1';
          // if ('fMaximumQty' in orderLine) orderLine.fMaximumQty = Number(orderLine.fMaximumQty);
          if ('bLienPricingItem' in orderLine) orderLine.bLienPricingItem = orderLine.bLienPricingItem === '1';
          // if ('lCallLienStepKey' in orderLine) orderLine.lCallLienStepKey = Number(orderLine.lCallLienStepKey);
          if ('bNoNegativePrice' in orderLine) orderLine.bNoNegativePrice = orderLine.bNoNegativePrice === '1';
          // if ('fSecondaryQty' in orderLine) orderLine.fSecondaryQty = Number(orderLine.fSecondaryQty);
          if ('bSecondaryFlatRate' in orderLine) orderLine.bSecondaryFlatRate = orderLine.bSecondaryFlatRate === '1';
          // if ('lSecondaryUnitTypeKey' in orderLine) orderLine.lSecondaryUnitTypeKey = Number(orderLine.lSecondaryUnitTypeKey);
          // if ('scTertiaryRate' in orderLine) orderLine.scTertiaryRate = Number(orderLine.scTertiaryRate);
          if ('bTertiaryFlatRate' in orderLine) orderLine.bTertiaryFlatRate = orderLine.bTertiaryFlatRate === '1';
          // if ('lTertiaryUnitTypeKey' in orderLine) orderLine.lTertiaryUnitTypeKey = Number(orderLine.lTertiaryUnitTypeKey);
          if ('pTertiaryQtyApplied' in orderLine) orderLine.pTertiaryQtyApplied = Number(orderLine.pTertiaryQtyApplied);
          if ('bCompounded' in orderLine) orderLine.bCompounded = orderLine.bCompounded === '1';
          // if ('ch10LocationGL' in orderLine) orderLine.ch10LocationGL = orderLine.ch10LocationGL;
          if ('bSurchargeable' in orderLine) orderLine.bSurchargeable = orderLine.bSurchargeable === '1';
          // if ('sInitialUnit' in orderLine) orderLine.sInitialUnit = orderLine.sInitialUnit;
          // if ('sSecondaryUnit' in orderLine) orderLine.sSecondaryUnit = orderLine.sSecondaryUnit;
          // if ('sTertiaryUnit' in orderLine) orderLine.sTertiaryUnit = orderLine.sTertiaryUnit;

          return orderLine;
        });
      }
    })
    .success(props.callback);
  },

  TOPSCALL__recalculatePricing (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'Recalculate',
      data: props.call
    })
    .responseMiddleware(response => {
      // if ('TaxRate' in response) response.TaxRate = Number(response.TaxRate);
      // if ('TaxRateOverride' in response) response.TaxRateOverride = Number(response.TaxRateOverride);
      // if ('DiscountPct' in response) response.DiscountPct = Number(response.DiscountPct);
      // if ('Total' in response) response.Total = Number(response.Total);
      // if ('TaxTotal' in response) response.TaxTotal = Number(response.TaxTotal);
      // if ('DiscountTotal' in response) response.DiscountTotal = Number(response.DiscountTotal);

      if ('TowOrderLines' in response) {
        response.TowOrderLines = response.TowOrderLines.map(orderLine => {
          // orderLine.uuid = `CalculateOrderPricingForNewCall_${orderLine.lOrderLineKey}`;

          // if ('lOrderLineKey' in orderLine) orderLine.lOrderLineKey = Number(orderLine.lOrderLineKey);
          // if ('lCallKey' in orderLine) orderLine.lCallKey = Number(orderLine.lCallKey);
          // if ('lDispatchKey' in orderLine) orderLine.lDispatchKey = Number(orderLine.lDispatchKey);
          // if ('lServicePricingTypeKey' in orderLine) orderLine.lServicePricingTypeKey = Number(orderLine.lServicePricingTypeKey);
          // if ('lServiceKey' in orderLine) orderLine.lServiceKey = Number(orderLine.lServiceKey);
          // if ('tDisplayOrder' in orderLine) orderLine.tDisplayOrder = Number(orderLine.tDisplayOrder);
          if ('pQty' in orderLine) orderLine.pQty = Number(orderLine.pQty);
          // if ('lInitialUnitTypeKey' in orderLine) orderLine.lInitialUnitTypeKey = Number(orderLine.lInitialUnitTypeKey);
          // if ('fInitialQty' in orderLine) orderLine.fInitialQty = Number(orderLine.fInitialQty);
          // if ('scInitialRate' in orderLine) orderLine.scInitialRate = Number(orderLine.scInitialRate);
          if ('bInitialFlatRate' in orderLine) orderLine.bInitialFlatRate = orderLine.bInitialFlatRate === '1';
          // if ('scSecondaryRate' in orderLine) orderLine.scSecondaryRate = Number(orderLine.scSecondaryRate);
          if ('bRatesOverridable' in orderLine) orderLine.bRatesOverridable = orderLine.bRatesOverridable === '1';
          // if ('vc100Description' in orderLine) orderLine.vc100Description = orderLine.vc100Description;
          if ('bRetow' in orderLine) orderLine.bRetow = orderLine.bRetow === '1';
          if ('bTaxable' in orderLine) orderLine.bTaxable = orderLine.bTaxable === '1';
          if ('bDiscountable' in orderLine) orderLine.bDiscountable = orderLine.bDiscountable === '1';
          if ('bCommissionable' in orderLine) orderLine.bCommissionable = orderLine.bCommissionable === '1';
          if ('fInitialQtyApplied' in orderLine) orderLine.fInitialQtyApplied = Number(orderLine.fInitialQtyApplied);
          if ('pSecondaryQtyApplied' in orderLine) orderLine.pSecondaryQtyApplied = Number(orderLine.pSecondaryQtyApplied);
          if ('tcTotalPrice' in orderLine) orderLine.tcTotalPrice = Number(orderLine.tcTotalPrice);
          if ('bCalculated' in orderLine) orderLine.bCalculated = orderLine.bCalculated === '1';
          // if ('fMinimumQty' in orderLine) orderLine.fMinimumQty = Number(orderLine.fMinimumQty);
          if ('lUserKey' in orderLine) orderLine.lUserKey = Number(orderLine.lUserKey);
          // if ('dLastModified' in orderLine) orderLine.dLastModified = orderLine.dLastModified;
          if ('bActive' in orderLine) orderLine.bActive = orderLine.bActive === '1';
          // if ('fMaximumQty' in orderLine) orderLine.fMaximumQty = Number(orderLine.fMaximumQty);
          if ('bLienPricingItem' in orderLine) orderLine.bLienPricingItem = orderLine.bLienPricingItem === '1';
          // if ('lCallLienStepKey' in orderLine) orderLine.lCallLienStepKey = Number(orderLine.lCallLienStepKey);
          if ('bNoNegativePrice' in orderLine) orderLine.bNoNegativePrice = orderLine.bNoNegativePrice === '1';
          // if ('fSecondaryQty' in orderLine) orderLine.fSecondaryQty = Number(orderLine.fSecondaryQty);
          if ('bSecondaryFlatRate' in orderLine) orderLine.bSecondaryFlatRate = orderLine.bSecondaryFlatRate === '1';
          // if ('lSecondaryUnitTypeKey' in orderLine) orderLine.lSecondaryUnitTypeKey = Number(orderLine.lSecondaryUnitTypeKey);
          // if ('scTertiaryRate' in orderLine) orderLine.scTertiaryRate = Number(orderLine.scTertiaryRate);
          if ('bTertiaryFlatRate' in orderLine) orderLine.bTertiaryFlatRate = orderLine.bTertiaryFlatRate === '1';
          // if ('lTertiaryUnitTypeKey' in orderLine) orderLine.lTertiaryUnitTypeKey = Number(orderLine.lTertiaryUnitTypeKey);
          if ('pTertiaryQtyApplied' in orderLine) orderLine.pTertiaryQtyApplied = Number(orderLine.pTertiaryQtyApplied);
          if ('bCompounded' in orderLine) orderLine.bCompounded = orderLine.bCompounded === '1';
          // if ('ch10LocationGL' in orderLine) orderLine.ch10LocationGL = orderLine.ch10LocationGL;
          if ('bSurchargeable' in orderLine) orderLine.bSurchargeable = orderLine.bSurchargeable === '1';
          // if ('sInitialUnit' in orderLine) orderLine.sInitialUnit = orderLine.sInitialUnit;
          // if ('sSecondaryUnit' in orderLine) orderLine.sSecondaryUnit = orderLine.sSecondaryUnit;
          // if ('sTertiaryUnit' in orderLine) orderLine.sTertiaryUnit = orderLine.sTertiaryUnit;

          return orderLine;
        });
      }
    })
    .success(props.success);
  },

  TOPSCALL__getOrderPricingForNew (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CalculateOrderPricingForNewCall',
      data: {
        lTowTypeKey: props.towTypeKey,
        lSubterminalKey: props.subterminalKey,
        lCustomerKey: props.customerKey,
        TaxRateOverride: props.taxRateOverride,
        DiscountPct: props.discountPercent,
        TowOrderLines: props.orderLines
      }
    })
    .responseMiddleware(response => {
      // if ('TaxRate' in response) response.TaxRate = Number(response.TaxRate);
      // if ('TaxRateOverride' in response) response.TaxRateOverride = Number(response.TaxRateOverride);
      // if ('DiscountPct' in response) response.DiscountPct = Number(response.DiscountPct);
      // if ('Total' in response) response.Total = Number(response.Total);
      // if ('TaxTotal' in response) response.TaxTotal = Number(response.TaxTotal);
      // if ('DiscountTotal' in response) response.DiscountTotal = Number(response.DiscountTotal);

      if ('TowOrderLines' in response) {
        response.TowOrderLines = response.TowOrderLines.map(orderLine => {
          // orderLine.uuid = `CalculateOrderPricingForNewCall_${orderLine.lOrderLineKey}`;

          // if ('lOrderLineKey' in orderLine) orderLine.lOrderLineKey = Number(orderLine.lOrderLineKey);
          // if ('lCallKey' in orderLine) orderLine.lCallKey = Number(orderLine.lCallKey);
          // if ('lDispatchKey' in orderLine) orderLine.lDispatchKey = Number(orderLine.lDispatchKey);
          // if ('lServicePricingTypeKey' in orderLine) orderLine.lServicePricingTypeKey = Number(orderLine.lServicePricingTypeKey);
          // if ('lServiceKey' in orderLine) orderLine.lServiceKey = Number(orderLine.lServiceKey);
          // if ('tDisplayOrder' in orderLine) orderLine.tDisplayOrder = Number(orderLine.tDisplayOrder);
          if ('pQty' in orderLine) orderLine.pQty = Number(orderLine.pQty);
          // if ('lInitialUnitTypeKey' in orderLine) orderLine.lInitialUnitTypeKey = Number(orderLine.lInitialUnitTypeKey);
          // if ('fInitialQty' in orderLine) orderLine.fInitialQty = Number(orderLine.fInitialQty);
          // if ('scInitialRate' in orderLine) orderLine.scInitialRate = Number(orderLine.scInitialRate);
          if ('bInitialFlatRate' in orderLine) orderLine.bInitialFlatRate = orderLine.bInitialFlatRate === '1';
          // if ('scSecondaryRate' in orderLine) orderLine.scSecondaryRate = Number(orderLine.scSecondaryRate);
          if ('bRatesOverridable' in orderLine) orderLine.bRatesOverridable = orderLine.bRatesOverridable === '1';
          // if ('vc100Description' in orderLine) orderLine.vc100Description = orderLine.vc100Description;
          if ('bRetow' in orderLine) orderLine.bRetow = orderLine.bRetow === '1';
          if ('bTaxable' in orderLine) orderLine.bTaxable = orderLine.bTaxable === '1';
          if ('bDiscountable' in orderLine) orderLine.bDiscountable = orderLine.bDiscountable === '1';
          if ('bCommissionable' in orderLine) orderLine.bCommissionable = orderLine.bCommissionable === '1';
          if ('fInitialQtyApplied' in orderLine) orderLine.fInitialQtyApplied = Number(orderLine.fInitialQtyApplied);
          if ('pSecondaryQtyApplied' in orderLine) orderLine.pSecondaryQtyApplied = Number(orderLine.pSecondaryQtyApplied);
          if ('tcTotalPrice' in orderLine) orderLine.tcTotalPrice = Number(orderLine.tcTotalPrice);
          if ('bCalculated' in orderLine) orderLine.bCalculated = orderLine.bCalculated === '1';
          // if ('fMinimumQty' in orderLine) orderLine.fMinimumQty = Number(orderLine.fMinimumQty);
          if ('lUserKey' in orderLine) orderLine.lUserKey = Number(orderLine.lUserKey);
          // if ('dLastModified' in orderLine) orderLine.dLastModified = orderLine.dLastModified;
          if ('bActive' in orderLine) orderLine.bActive = orderLine.bActive === '1';
          // if ('fMaximumQty' in orderLine) orderLine.fMaximumQty = Number(orderLine.fMaximumQty);
          if ('bLienPricingItem' in orderLine) orderLine.bLienPricingItem = orderLine.bLienPricingItem === '1';
          // if ('lCallLienStepKey' in orderLine) orderLine.lCallLienStepKey = Number(orderLine.lCallLienStepKey);
          if ('bNoNegativePrice' in orderLine) orderLine.bNoNegativePrice = orderLine.bNoNegativePrice === '1';
          // if ('fSecondaryQty' in orderLine) orderLine.fSecondaryQty = Number(orderLine.fSecondaryQty);
          if ('bSecondaryFlatRate' in orderLine) orderLine.bSecondaryFlatRate = orderLine.bSecondaryFlatRate === '1';
          // if ('lSecondaryUnitTypeKey' in orderLine) orderLine.lSecondaryUnitTypeKey = Number(orderLine.lSecondaryUnitTypeKey);
          // if ('scTertiaryRate' in orderLine) orderLine.scTertiaryRate = Number(orderLine.scTertiaryRate);
          if ('bTertiaryFlatRate' in orderLine) orderLine.bTertiaryFlatRate = orderLine.bTertiaryFlatRate === '1';
          // if ('lTertiaryUnitTypeKey' in orderLine) orderLine.lTertiaryUnitTypeKey = Number(orderLine.lTertiaryUnitTypeKey);
          if ('pTertiaryQtyApplied' in orderLine) orderLine.pTertiaryQtyApplied = Number(orderLine.pTertiaryQtyApplied);
          if ('bCompounded' in orderLine) orderLine.bCompounded = orderLine.bCompounded === '1';
          // if ('ch10LocationGL' in orderLine) orderLine.ch10LocationGL = orderLine.ch10LocationGL;
          if ('bSurchargeable' in orderLine) orderLine.bSurchargeable = orderLine.bSurchargeable === '1';
          // if ('sInitialUnit' in orderLine) orderLine.sInitialUnit = orderLine.sInitialUnit;
          // if ('sSecondaryUnit' in orderLine) orderLine.sSecondaryUnit = orderLine.sSecondaryUnit;
          // if ('sTertiaryUnit' in orderLine) orderLine.sTertiaryUnit = orderLine.sTertiaryUnit;

          return orderLine;
        });
      }
    })
    .success(props.callback);
  },

  TOPSCALL__getSubcompanyDetails (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetSubcompanyDetails',
      data: { lSubterminalKey: props.subterminalKey }
    })
    .responseMiddleware(response => {
      if ('fTaxRate' in response) response.fTaxRate = Number(response.fTaxRate);
      if ('lCreditCardProcessingTypeKey' in response) response.lCreditCardProcessingTypeKey = Number(response.lCreditCardProcessingTypeKey);
      if ('gcLatitude' in response) response.gcLatitude = Number(response.gcLatitude);
      if ('gcLongitude' in response) response.gcLongitude = Number(response.gcLongitude);
    })
    .success(props.success);
  },

  TOPSCALL__findCustomersForNew (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'FindCustomersForNewCall',
      data: props.data
    })
    .success(props.callback);
  },

  TOPSCALL__tagToVIN (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'TagToVIN',
      data: {
        State: props.state,
        Tag: props.tag
      }
    })
    .success(props.success);
  },

  TOPSCALL__decodeVIN (context, props) {
    if (is.empty(props.vin)) return;

    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'DecodeVIN',
      data: { VIN: props.vin }
    })
    .success(props.callback);
  },

  TOPSCALL__update (context, props) {
    if (is.empty(props.vin)) return;

    props.call.CallKey = props.call.lCallKey;

    forEach(props.call.Holds, hold => {
      hold.bHoldOn ? '1' : '0';
    });

    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'Update',
      lastRead: props.lastRead,
      data: prepareApiData(props.call)
    })
    .requestMiddleware(params => {
      // Debug: Log the entire params structure to see where vc255DelimitedInfo is located
      console.log('TOPSCALL__update request params:', JSON.stringify(params, null, 2));

      // Check multiple possible locations for vc255DelimitedInfo
      const locations = [
        { path: 'params.Data.vc255DelimitedInfo', value: params.Data?.vc255DelimitedInfo },
        { path: 'params.vc255DelimitedInfo', value: params.vc255DelimitedInfo },
        { path: 'params.Data.CALvc255DelimitedInfo', value: params.Data?.CALvc255DelimitedInfo },
        { path: 'params.CALvc255DelimitedInfo', value: params.CALvc255DelimitedInfo }
      ];

      locations.forEach(location => {
        if (location.value) {
          console.log(`Found vc255DelimitedInfo at ${location.path}:`, location.value);
          const info = location.value.toString();
          if (!info.endsWith(';')) {
            // Update the field at the correct location
            if (location.path === 'params.Data.vc255DelimitedInfo') {
              params.Data.vc255DelimitedInfo = `${info};`;
            } else if (location.path === 'params.vc255DelimitedInfo') {
              params.vc255DelimitedInfo = `${info};`;
            } else if (location.path === 'params.Data.CALvc255DelimitedInfo') {
              params.Data.CALvc255DelimitedInfo = `${info};`;
            } else if (location.path === 'params.CALvc255DelimitedInfo') {
              params.CALvc255DelimitedInfo = `${info};`;
            }
            console.log(`Updated ${location.path} to:`, `${info};`);
          }
        }
      });
    })
    .success(props.callback)
    .fail(props.failCallback);
  },

  TOPSCALL__read (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'ReadCall',
      data: {
        CallKey: props.callKey,
        DetailsSections: castArray(get(props, 'details', ''))
      }
    })
    .responseMiddleware(response => {
      if ('lCallKey' in response) response.lCallKey = Number(response.lCallKey);
      if ('lCallStatusTypeKey' in response) response.lCallStatusTypeKey = Number(response.lCallStatusTypeKey);
      if ('dExpirationDate' in response) response.dExpirationDate = simpleDate(response.dExpirationDate);
      if ('lUserKey_CallTaken' in response) response.lUserKey_CallTaken = Number(response.lUserKey_CallTaken);
      if ('bNoCharge' in response) response.bNoCharge = response.bNoCharge === '1';
      if ('bPortalToPortal' in response) response.bPortalToPortal = response.bPortalToPortal === '1';
      if ('bSecondCommission' in response) response.bSecondCommission = response.bSecondCommission === '1';
      if ('bMileageRequired' in response) response.bMileageRequired = response.bMileageRequired === '1';
      if ('fDiscountPct' in response) response.fDiscountPct = Number(response.fDiscountPct);
      if ('lTowTypeKey' in response) response.lTowTypeKey = Number(response.lTowTypeKey);

      if (has(response, 'Retow.dCallTaken')) response.Retow.dCallTaken = fullDateTime(response.Retow.dCallTaken);
      if (has(response, 'Retow.dETA')) response.Retow.dETA = fullDateTime(response.Retow.dETA);
      if (has(response, 'Retow.dAppointment')) response.Retow.dAppointment = fullDateTime(response.Retow.dAppointment);

      if ('TowOrderLines' in response) {
        response.TowOrderLines = response.TowOrderLines.map(orderLine => {
          // orderLine.uuid = `ReadCall_${orderLine.lOrderLineKey}`;

          if ('lOrderLineKey' in orderLine) orderLine.lOrderLineKey = Number(orderLine.lOrderLineKey);
          if ('lCallKey' in orderLine) orderLine.lCallKey = Number(orderLine.lCallKey);
          if ('lDispatchKey' in orderLine) orderLine.lDispatchKey = Number(orderLine.lDispatchKey);
          if ('lServicePricingTypeKey' in orderLine) orderLine.lServicePricingTypeKey = Number(orderLine.lServicePricingTypeKey);
          if ('lServiceKey' in orderLine) orderLine.lServiceKey = Number(orderLine.lServiceKey);
          if ('tDisplayOrder' in orderLine) orderLine.tDisplayOrder = Number(orderLine.tDisplayOrder);
          if ('pQty' in orderLine) orderLine.pQty = Number(orderLine.pQty);
          // if ('lInitialUnitTypeKey' in orderLine) orderLine.lInitialUnitTypeKey = Number(orderLine.lInitialUnitTypeKey);
          // if ('fInitialQty' in orderLine) orderLine.fInitialQty = Number(orderLine.fInitialQty);
          // if ('scInitialRate' in orderLine) orderLine.scInitialRate = Number(orderLine.scInitialRate);
          if ('bInitialFlatRate' in orderLine) orderLine.bInitialFlatRate = orderLine.bInitialFlatRate === '1';
          // if ('scSecondaryRate' in orderLine) orderLine.scSecondaryRate = Number(orderLine.scSecondaryRate);
          if ('bRatesOverridable' in orderLine) orderLine.bRatesOverridable = orderLine.bRatesOverridable === '1';
          // if ('vc100Description' in orderLine) orderLine.vc100Description = orderLine.vc100Description;
          if ('bRetow' in orderLine) orderLine.bRetow = orderLine.bRetow === '1';
          if ('bTaxable' in orderLine) orderLine.bTaxable = orderLine.bTaxable === '1';
          if ('bDiscountable' in orderLine) orderLine.bDiscountable = orderLine.bDiscountable === '1';
          if ('bCommissionable' in orderLine) orderLine.bCommissionable = orderLine.bCommissionable === '1';
          if ('fInitialQtyApplied' in orderLine) orderLine.fInitialQtyApplied = Number(orderLine.fInitialQtyApplied);
          if ('pSecondaryQtyApplied' in orderLine) orderLine.pSecondaryQtyApplied = Number(orderLine.pSecondaryQtyApplied);
          if ('tcTotalPrice' in orderLine) orderLine.tcTotalPrice = Number(orderLine.tcTotalPrice);
          if ('bCalculated' in orderLine) orderLine.bCalculated = orderLine.bCalculated === '1';
          // if ('fMinimumQty' in orderLine) orderLine.fMinimumQty = Number(orderLine.fMinimumQty);
          if ('lUserKey' in orderLine) orderLine.lUserKey = Number(orderLine.lUserKey);
          // if ('dLastModified' in orderLine) orderLine.dLastModified = orderLine.dLastModified;
          if ('bActive' in orderLine) orderLine.bActive = orderLine.bActive === '1';
          // if ('fMaximumQty' in orderLine) orderLine.fMaximumQty = Number(orderLine.fMaximumQty);
          if ('bLienPricingItem' in orderLine) orderLine.bLienPricingItem = orderLine.bLienPricingItem === '1';
          // if ('lCallLienStepKey' in orderLine) orderLine.lCallLienStepKey = Number(orderLine.lCallLienStepKey);
          if ('bNoNegativePrice' in orderLine) orderLine.bNoNegativePrice = orderLine.bNoNegativePrice === '1';
          // if ('fSecondaryQty' in orderLine) orderLine.fSecondaryQty = Number(orderLine.fSecondaryQty);
          if ('bSecondaryFlatRate' in orderLine) orderLine.bSecondaryFlatRate = orderLine.bSecondaryFlatRate === '1';
          // if ('lSecondaryUnitTypeKey' in orderLine) orderLine.lSecondaryUnitTypeKey = Number(orderLine.lSecondaryUnitTypeKey);
          // if ('scTertiaryRate' in orderLine) orderLine.scTertiaryRate = Number(orderLine.scTertiaryRate);
          if ('bTertiaryFlatRate' in orderLine) orderLine.bTertiaryFlatRate = orderLine.bTertiaryFlatRate === '1';
          // if ('lTertiaryUnitTypeKey' in orderLine) orderLine.lTertiaryUnitTypeKey = Number(orderLine.lTertiaryUnitTypeKey);
          if ('pTertiaryQtyApplied' in orderLine) orderLine.pTertiaryQtyApplied = Number(orderLine.pTertiaryQtyApplied);
          if ('bCompounded' in orderLine) orderLine.bCompounded = orderLine.bCompounded === '1';
          // if ('ch10LocationGL' in orderLine) orderLine.ch10LocationGL = orderLine.ch10LocationGL;
          if ('bSurchargeable' in orderLine) orderLine.bSurchargeable = orderLine.bSurchargeable === '1';
          // if ('sInitialUnit' in orderLine) orderLine.sInitialUnit = orderLine.sInitialUnit;
          // if ('sSecondaryUnit' in orderLine) orderLine.sSecondaryUnit = orderLine.sSecondaryUnit;
          // if ('sTertiaryUnit' in orderLine) orderLine.sTertiaryUnit = orderLine.sTertiaryUnit;

          return orderLine;
        });
      }

      if ('Dispatches' in response) {
        response.Dispatches = response.Dispatches.map(dispatch => {
          if ('lDispatchKey' in dispatch) dispatch.lDispatchKey = Number(dispatch.lDispatchKey);
          if ('lCallKey' in dispatch) dispatch.lCallKey = Number(dispatch.lCallKey);
          if ('lDriverKey' in dispatch) dispatch.lDriverKey = Number(dispatch.lDriverKey);
          if ('lTruckKey' in dispatch) dispatch.lTruckKey = Number(dispatch.lTruckKey);
          if ('lDispatchStatusTypeKey' in dispatch) dispatch.lDispatchStatusTypeKey = Number(dispatch.lDispatchStatusTypeKey);
          if ('lUserKey_Assigned' in dispatch) dispatch.lUserKey_Assigned = Number(dispatch.lUserKey_Assigned);
          if ('lUserKey_Dispatched' in dispatch) dispatch.lUserKey_Dispatched = Number(dispatch.lUserKey_Dispatched);
          if ('lUserKey_Acknowledged' in dispatch) dispatch.lUserKey_Acknowledged = Number(dispatch.lUserKey_Acknowledged);
          if ('lUserKey_Arrived' in dispatch) dispatch.lUserKey_Arrived = Number(dispatch.lUserKey_Arrived);
          if ('lUserKey_Hooked' in dispatch) dispatch.lUserKey_Hooked = Number(dispatch.lUserKey_Hooked);
          if ('lUserKey_Dropped' in dispatch) dispatch.lUserKey_Dropped = Number(dispatch.lUserKey_Dropped);
          if ('lUserKey_Completed' in dispatch) dispatch.lUserKey_Completed = Number(dispatch.lUserKey_Completed);
          if ('lTotalMileage' in dispatch) dispatch.lTotalMileage = Number(dispatch.lTotalMileage);
          if ('bRetow' in dispatch) dispatch.bRetow = dispatch.bRetow === '1';
          if ('lOdometer_Acknowledged' in dispatch) dispatch.lOdometer_Acknowledged = Number(dispatch.lOdometer_Acknowledged);
          if ('lOdometer_Arrived' in dispatch) dispatch.lOdometer_Arrived = Number(dispatch.lOdometer_Arrived);
          if ('lOdometer_Hooked' in dispatch) dispatch.lOdometer_Hooked = Number(dispatch.lOdometer_Hooked);
          if ('lOdometer_Dropped' in dispatch) dispatch.lOdometer_Dropped = Number(dispatch.lOdometer_Dropped);
          if ('lOdometer_Completed' in dispatch) dispatch.lOdometer_Completed = Number(dispatch.lOdometer_Completed);

          return dispatch;
        });
      }

      if ('InspectionItems' in response) {
        response.InspectionItems = response.InspectionItems.map(inspectionItem => {
          if ('lCallKey' in inspectionItem) inspectionItem.lCallKey = Number(inspectionItem.lCallKey);
          if ('lItemKey' in inspectionItem) inspectionItem.lItemKey = Number(inspectionItem.lItemKey);
          if ('lOrder' in inspectionItem) inspectionItem.lOrder = Number(inspectionItem.lOrder);

          return inspectionItem;
        });
      }

      if ('Legs' in response) {
        response.Legs = response.Legs.map(leg => {
          if ('lCall' in leg) leg.lCall = Number(leg.lCall);
          if ('tOrder' in leg) leg.tOrder = Number(leg.tOrder);
          if ('lMiles' in leg) leg.lMiles = Number(leg.lMiles);

          return leg;
        });
      }
    })
    .success(props.success)
    .fail(props.fail)
    .always(props.always);
  },

  TOPSCALL__getDriverTruckToAssign (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetDriverTruckInfoForAssigning',
      data: {
        DriverStatuses: props.driverStatuses,
        TruckStatuses: props.truckStatuses,
        NotBusy: get(props, 'notBusy', true)
      }
    })
    .responseMiddleware(response => {
      forEach(response.Drivers, driver => {
        driver.Key = toNumber(driver.Key);
        driver.Count = toNumber(driver.Count);
      });

      forEach(response.Trucks, truck => {
        truck.Key = toNumber(truck.Key);
        truck.Count = toNumber(truck.Count);
        truck.DispatchStatusKey = toNumber(truck.DispatchStatusKey);
      });
    })
    .success(props.callback);
  },

  TOPSCALL__getDataToAssign (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetCallInfoForAssigning',
      data: {
        CallKey: props.callKey,
        Geocode: true
      }
    })
    .responseMiddleware(response => {
      response.Retow = response.Retow === 'true';

      response.SubcompanyLat = toNumber(response.SubcompanyLat);
      response.SubcompanyLon = toNumber(response.SubcompanyLon);

      if (has(response, 'Location.Lat')) {
        response.Location.Lat = toNumber(response.Location.Lat);
      }

      if (has(response, 'Location.Lon')) {
        response.Location.Lon = toNumber(response.Location.Lon);
      }

      if (has(response, 'Destination.Lat')) {
        response.Destination.Lat = toNumber(response.Destination.Lat);
      }

      if (has(response, 'Destination.Lon')) {
        response.Destination.Lon = toNumber(response.Destination.Lon);
      }
    })
    .success(props.callback);
  },

  TOPSCALL__getDefaultsForNewPayment (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetDefaultsForNewPayment',
      data: {
        CallKey: props.callKey
      }
    })
    .responseMiddleware(response => {
      if ('tcAmount' in response) response.tcAmount = Number(response.tcAmount);
      // if ('dReceived' in response) response.dReceived = response.dReceived;
      if ('lCustomerKey' in response) response.lCustomerKey = Number(response.lCustomerKey);
      if ('lSubterminalKey' in response) response.lSubterminalKey = Number(response.lSubterminalKey);
      if ('lPaymentTypeKey' in response) response.lPaymentTypeKey = Number(response.lPaymentTypeKey);
      if ('lReceiptTypeKey' in response) response.lReceiptTypeKey = Number(response.lReceiptTypeKey);
      if ('lReceivedBy' in response) response.lReceivedBy = Number(response.lReceivedBy);
    })
    .success(props.success);
  },

  TOPSCALL__assign (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'AssignCall',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        Code: props.driverCode,
        Number: props.truckNumber
      }
    })
    .success(props.callback);
  },

  TOPSCALL__assignAdditional (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'AssignAdditional',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        Code: props.driverCode,
        Number: props.truckNumber
      }
    })
    .success(props.callback);
  },

  TOPSCALL__holdStep (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienPutStepOnHold',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        Hours: get(props, 'hours', ''),
        UntilDateTime: get(props, 'until', '')
      }
    })
    .success(props.callback);
  },

  TOPSCALL__activateStep (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienActivateStep',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey
      }
    })
    .success(props.callback);
  },

  TOPSCALL__processTask (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienProcessStepTasks',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        CallStepKey: props.stepKey,
        TaskKey: get(props, 'taskKey', ''),
        LettersSentDate: get(props, 'lettersSentDate', ''),
        LabelAlias: get(props, 'labelFormat', ''),
        StartLabel: get(props, 'startLabel', ''),
        SetDate: get(props, 'setDate', ''),
        CertificationNum: get(props, 'certificationNumber', ''),
        CertificationSeed: get(props, 'certificationSeed', ''),
        Other: get(props, 'targetProcessKey', '')
      }
    })
    .success(props.callback);
  },

  TOPSCALL__undoStep (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienUndoStep',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        RemoveLienPricingItems: get(props, 'removePricingItems', true)
      }
    })
    .success(props.callback);
  },

  TOPSCALL__addLetter (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienAddLetter',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        lLienLetterKey: props.letterKey,
        lLienKey: props.lienKey,
        dDateSent: props.dateSent,
        bActive: props.active,
        lOwnerKey: get(props, 'ownerKey', ''),
        lLienTaskKey: get(props, 'taskKey', ''),
        vc20CertificationNum: get(props, 'certificationNum', ''),
        dReturnReceiptDate: get(props, 'returnReceiptDate', ''),
        vc50Notes: get(props, 'notes', ''),
        lBatchKey: get(props, 'batchKey', '')
      }
    })
    .success(props.callback);
  },

  TOPSCALL__skipStep (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienSkipStep',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey
      }
    })
    .success(props.callback);
  },

  TOPSCALL__addOwner (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienAddOwner',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        lOwnerKey: props.ownerKey,
        vc50Name: props.name,
        lLienKey: props.lienKey,
        lOwnerTypeKey: props.ownerTypeKey,
        vc30Address1: props.address1,
        vc30Address2: get(props, 'address2', ''),
        vc30City: props.city,
        ch2StateKey: props.stateKey,
        vc10ZipCode: props.zipCode,
        bActive: props.active
      }
    })
    .success(props.callback);
  },

  TOPSCALL__terminateLien (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienTerminate',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        RemoveLienPricingItems: get(props, 'removePricingItems', false)
      }
    })
    .success(props.callback);
  },

  TOPSCALL__confirm (context, props) {
    new StandardRequest(context, {
      showGlobalNotification: false,
      noun: 'TOPSCall',
      verb: 'Confirm',
      data: {
        CallKey: props.callKey
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__reconcileTow (context, props) {
    let data = { CallKey: props.callKey };

    if ('Retow' in props) {
      set(data, 'Retow', props.retow);
    }

    if ('bothTowAndRetow' in props) {
      set(data, 'BothTowAndRetow', props.bothTowAndRetow);
    }

    new StandardRequest(context, {
      showGlobalNotification: false,
      noun: 'TOPSCall',
      verb: 'ReconcileTow',
      data: data
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__reconcilePayment (context, props) {
    new StandardRequest(context, {
      showGlobalNotification: false,
      noun: 'TOPSCall',
      verb: 'ReconcilePayment',
      data: {
        CallKey: props.callKey,
        lPaymentKey: props.paymentKey
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__lienCompleteStep (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'LienCompleteStep',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__getConditionReportLinks (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetConditionReportLinks',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__getCCPaymentInfo (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetCCPaymentInfo',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        TowPayment: props.isTowPayment
      }
    })
    .responseMiddleware(response => {
      response.MaxAmount = Number(response.MaxAmount);
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__createTowPayNotPresentPayment (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CreateTowPayNotPresentPayment',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        TowPayment: props.isTowPayment,
        Amount: props.amount,
        PaymentMethodID: props.paymentMethodId,
        Name: props.name,
        Brand: props.brand,
        Expiry: props.expiry,
        LastFour: props.lastFour,
        Note: props.note,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG',
        ReceiptType: props.receiptType,
        EmployeeKey: props.employeeKey
      }
    })
    .responseMiddleware(response => {
      if ('PaymentKey' in response) response.PaymentKey = Number(response.PaymentKey);
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__sendTowPayIntentToReader (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'SendTowPayIntentToReader',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        ReaderID: props.readerID,
        ReaderType: props.readerType,
        TowPayment: props.isTowPayment,
        Amount: props.amount,
        Note: props.note,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG',
        ReceiptType: props.receiptType,
        EmployeeKey: props.employeeKey
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__sendLinkToPay (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'SendLinkToPay',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        EmailPhone: props.emailPhone,
        TowPayment: props.isTowPayment,
        Amount: props.amount,
        Note: props.note,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG',
        ReceiptType: props.receiptType,
        EmployeeKey: props.employeeKey
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__cancelTowPayReaderIntent (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CancelTowPayReaderIntent',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        IntentID: props.intentId,
        ReaderID: props.readerId,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG'
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success)
    .fail(props.fail)
    .always(props.always);
  },

  TOPSCALL__refundTowPayPayment (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'RefundTowPayPayment',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        Amount: props.amount,
        PaymentKey: props.paymentKey,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG'
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__getDMVCommunications (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetDMVCommunications',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG'
      }
    })
      .responseMiddleware(response => {
        // ...
      })
      .success(props.success)
      .fail(props.fail);
  },

  TOPSCALL__getDMVStates (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetAvailableDMVStates',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG'
      }
    })
      .responseMiddleware(response => {
        // ...
      })
      .success(props.success)
      .fail(props.fail);
  },

  TOPSCALL__requestDMV (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'RequestDMV',
      lastRead: props.lastRead,
      data: {
        CallKey: props.CallKey,
        State: props.State,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG'
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__findCustomersForSale (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'FindCustomersForSale',
      lastRead: props.lastRead,
      data: props.data
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__release (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'Release',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        lFinalDispositionTypeKey: props.finalDispositionTypeKey,
        DateOut: props.dateOut,
        Name: get(props, 'name', ''),
        Address1: get(props, 'address', ''),
        Address2: get(props, 'address2', ''),
        City: get(props, 'city', ''),
        State: get(props, 'state', ''),
        Zip: get(props, 'zip', ''),
        LicenseNum: get(props, 'licenseNum', ''),
        LicenseState: get(props, 'licenseState', ''),
        Phone: get(props, 'phone', ''),
        RemoveLienPricing: get(props, 'removeLienPricing', false),
        lSaleCustomerKey: get(props, 'saleCustomerKey', ''),
        SaleTaxRate: get(props, 'saleTaxRate', ''),
        TowPayments: get(props, 'towPayments', []),
        SalePayments: get(props, 'salePayments', []),
        SaleServices: get(props, 'appliedServices', [])
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__updateTowLocation (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'UpdateTowLocation',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        Lat: props.latitude,
        Lon: props.longitude,
        Retow: get(props, 'isRetow', false)
      }
    })
    .responseMiddleware(() => {})
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__calcMileageLoaded (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CalcMileageLoaded',
      lastRead: props.lastRead,
      data: {
        LocationLat: props.locationLatitude,
        LocationLon: props.locationLongitude,
        DestinationLat: props.destinationLatitude,
        DestinationLon: props.destinationLongitude
      }
    })
    .responseMiddleware(() => {})
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__calcMileageReturn (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CalcMileageReturn',
      lastRead: props.lastRead,
      data: {
        SubterminalKey: props.subterminalKey,
        DestinationLat: props.destinationLatitude,
        DestinationLon: props.destinationLongitude
      }
    })
    .responseMiddleware(() => {})
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__calcMileageUnloaded (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'CalcMileageUnloaded',
      lastRead: props.lastRead,
      data: {
        SubterminalKey: props.subterminalKey,
        LocationLat: props.locationLatitude,
        LocationLon: props.locationLongitude
      }
    })
    .responseMiddleware(() => {})
    .success(props.success)
    .fail(props.fail);
  },

  TOPSCALL__getCallInfoForReleasing (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCall',
      verb: 'GetCallInfoForReleasing',
      lastRead: props.lastRead,
      data: {
        CallKey: props.callKey,
        SaleCustomerKey: get(props, 'saleCustomerKey', '')
      }
    })
    .responseMiddleware(response => {
      if ('TowBalance' in response) response.TowBalance = Number(response.TowBalance);
      if ('SaleTaxRate' in response) response.SaleTaxRate = Number(response.SaleTaxRate);
      if ('ReleasePaymentRequired' in response) response.ReleasePaymentRequired = Number(response.ReleasePaymentRequired) === 1;
      if ('AskToReleaseHolds' in response) response.AskToReleaseHolds = Number(response.AskToReleaseHolds) === 1;
      if ('AskToTerminateLien' in response) response.AskToTerminateLien = Number(response.AskToTerminateLien) === 1;
      if ('AskToRemoveLienPricing' in response) response.AskToRemoveLienPricing = Number(response.AskToRemoveLienPricing) === 1;
      if ('ReleaseToOwnerOnly' in response) response.ReleaseToOwnerOnly = Number(response.ReleaseToOwnerOnly) === 1;

      if ('TowPayments' in response) {
        response.TowPayments.forEach(payment => {
          if ('lApplicationKey' in payment) payment.lApplicationKey = Number(payment.lApplicationKey);
          if ('lPaymentKey' in payment) payment.lPaymentKey = Number(payment.lPaymentKey);
          if ('lCallKey' in payment) payment.lCallKey = Number(payment.lCallKey);
          if ('lAppliedBy' in payment) payment.lAppliedBy = Number(payment.lAppliedBy);
          if ('tcAmount' in payment) payment.tcAmount = Number(payment.tcAmount);
          if ('bTow' in payment) payment.bTow = Number(payment.bTow) === 1;
          if ('lReceiptTypeKey' in payment) payment.lReceiptTypeKey = Number(payment.lReceiptTypeKey);
          if ('lPaymentTypeKey' in payment) payment.lPaymentTypeKey = Number(payment.lPaymentTypeKey);
          if ('lReceivedBy' in payment) payment.lReceivedBy = Number(payment.lReceivedBy);
          if ('lReconciledBy' in payment) payment.lReconciledBy = Number(payment.lReconciledBy);
          if ('lCreatedBy' in payment) payment.lCreatedBy = Number(payment.lCreatedBy);
          if ('bTransferredToFES' in payment) payment.bTransferredToFES = [1, '1', true, 'true'].includes(payment.bTransferredToFES);
          if ('bActive' in payment) payment.bActive = Number(payment.bActive) === 1;
          if ('lDepositedBy' in payment) payment.lDepositedBy = Number(payment.lDepositedBy);
          if ('lProcessingTypeKey' in payment) payment.lProcessingTypeKey = Number(payment.lProcessingTypeKey);
          if ('lCreditCardTypeKey' in payment) payment.lCreditCardTypeKey = Number(payment.lCreditCardTypeKey);
          if ('lCustomerKey' in payment) payment.lCustomerKey = Number(payment.lCustomerKey);
          if ('tcUnappliedAmount' in payment) payment.tcUnappliedAmount = Number(payment.tcUnappliedAmount);
          if ('lSubterminalKey' in payment) payment.lSubterminalKey = Number(payment.lSubterminalKey);
          if ('lCardStatusTypeKey' in payment) payment.lCardStatusTypeKey = Number(payment.lCardStatusTypeKey);
        });
      }

      if ('SalePayments' in response) {
        response.SalePayments.forEach(payment => {
          if ('lApplicationKey' in payment) payment.lApplicationKey = Number(payment.lApplicationKey);
          if ('lPaymentKey' in payment) payment.lPaymentKey = Number(payment.lPaymentKey);
          if ('lCallKey' in payment) payment.lCallKey = Number(payment.lCallKey);
          if ('lAppliedBy' in payment) payment.lAppliedBy = Number(payment.lAppliedBy);
          if ('tcAmount' in payment) payment.tcAmount = Number(payment.tcAmount);
          if ('bTow' in payment) payment.bTow = Number(payment.bTow) === 1;
          if ('lReceiptTypeKey' in payment) payment.lReceiptTypeKey = Number(payment.lReceiptTypeKey);
          if ('lPaymentTypeKey' in payment) payment.lPaymentTypeKey = Number(payment.lPaymentTypeKey);
          if ('lReceivedBy' in payment) payment.lReceivedBy = Number(payment.lReceivedBy);
          if ('lReconciledBy' in payment) payment.lReconciledBy = Number(payment.lReconciledBy);
          if ('lCreatedBy' in payment) payment.lCreatedBy = Number(payment.lCreatedBy);
          if ('bTransferredToFES' in payment) payment.bTransferredToFES = [1, '1', true, 'true'].includes(payment.bTransferredToFES);
          if ('bActive' in payment) payment.bActive = Number(payment.bActive) === 1;
          if ('lDepositedBy' in payment) payment.lDepositedBy = Number(payment.lDepositedBy);
          if ('lProcessingTypeKey' in payment) payment.lProcessingTypeKey = Number(payment.lProcessingTypeKey);
          if ('lCreditCardTypeKey' in payment) payment.lCreditCardTypeKey = Number(payment.lCreditCardTypeKey);
          if ('lCustomerKey' in payment) payment.lCustomerKey = Number(payment.lCustomerKey);
          if ('tcUnappliedAmount' in payment) payment.tcUnappliedAmount = Number(payment.tcUnappliedAmount);
          if ('lSubterminalKey' in payment) payment.lSubterminalKey = Number(payment.lSubterminalKey);
          if ('lCardStatusTypeKey' in payment) payment.lCardStatusTypeKey = Number(payment.lCardStatusTypeKey);
        });
      }

      if ('SaleServices' in response) {
        response.SaleServices.forEach(service => {
          if ('Key' in service) service.Key = Number(service.Key);
          if ('Taxable' in service) service.Taxable = Number(service.Taxable) === 1;
        });
      }
    })
    .success(props.success)
    .fail(props.fail);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
